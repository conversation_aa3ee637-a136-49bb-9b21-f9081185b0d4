import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/event_statistics_controller.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/formatted_currency.dart';

class EventStatistics extends StatefulWidget {
  final int? eventId;
  const EventStatistics({super.key, this.eventId});

  @override
  State<EventStatistics> createState() => _EventStatisticsState();
}

class _EventStatisticsState extends State<EventStatistics> {
  late final EventStatisticsController controller;

  @override
  void initState() {
    super.initState();
    controller = Get.put(EventStatisticsController());
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    await controller.fetchEventStatistics(eventId: widget.eventId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Event Statistics',
        
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.dark),
        actions: [
          IconButton(
            onPressed: _loadStatistics,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh Statistics',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoadingStatistics.value) {
          return _buildLoadingState();
        }

        if (controller.hasError.value) {
          return _buildErrorState();
        }

        return _buildStatisticsContent();
      }),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
          ),
          SizedBox(height: 16),
          Text(
            'Loading statistics...',
            style: TextStyle(
              color: AppColors.greyTextColor,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Error Loading Statistics',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.dark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.errorMessage.value,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.greyTextColor,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadStatistics,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsContent() {
    return RefreshIndicator(
      onRefresh: _loadStatistics,
      color: AppColors.primary,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildOverviewSection(),
            const SizedBox(height: 24),
            _buildRevenueSection(),
            const SizedBox(height: 24),
            _buildUserDemographicsSection(),
            const SizedBox(height: 24),
            _buildPlaceholderSections(),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.dashboard,
                  color: AppColors.primary,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Overview',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (controller.hasOverviewData)
              _buildOverviewGrid()
            else
              _buildNoDataMessage('No overview data available'),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildMetricCard(
          'Total Events',
          controller.totalEvents.toString(),
          Icons.event,
          AppColors.primary,
        ),
        _buildMetricCard(
          'Total Revenue',
          FormattedCurrency.getFormattedCurrency(controller.totalRevenue),
          Icons.attach_money,
          Colors.green,
        ),
        _buildMetricCard(
          'Tickets Sold',
          controller.totalTicketsSold.toString(),
          Icons.confirmation_number,
          Colors.blue,
        ),
        _buildMetricCard(
          'Total Attendees',
          controller.totalAttendees.toString(),
          Icons.people,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.slate, width: 1),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 32,
            color: color,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              // color: AppColors.dark,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRevenueSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: Colors.green,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'Revenue Breakdown',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    // color: AppColors.dark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (controller.hasRevenueData)
              _buildRevenueContent()
            else
              _buildNoDataMessage('No revenue data available'),
          ],
        ),
      ),
    );
  }

  Widget _buildRevenueContent() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                'Total Revenue',
                FormattedCurrency.getFormattedCurrency(controller.totalRevenue),
                Icons.monetization_on,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoTile(
                'Daily Average',
                FormattedCurrency.getFormattedCurrency(controller.averageDailyRevenue),
                Icons.calendar_today,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildInfoTile(
          'Total Transactions',
          controller.totalTransactions.toString(),
          Icons.receipt,
        ),
      ],
    );
  }

  Widget _buildUserDemographicsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(
                  Icons.people_outline,
                  color: Colors.purple,
                  size: 24,
                ),
                SizedBox(width: 8),
                Text(
                  'User Demographics',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    // color: AppColors.dark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (controller.hasUserDemographicsData)
              _buildUserDemographicsContent()
            else
              _buildNoDataMessage('No user demographics data available'),
          ],
        ),
      ),
    );
  }

  Widget _buildUserDemographicsContent() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                'Unique Customers',
                controller.uniqueCustomers.toString(),
                Icons.person,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoTile(
                'Repeat Customers',
                controller.repeatCustomers.toString(),
                Icons.repeat,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildInfoTile(
                'Repeat Rate',
                '${controller.repeatCustomerRate.toStringAsFixed(1)}%',
                Icons.trending_up,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildInfoTile(
                'Avg Tickets/User',
                controller.averageTicketsPerUser.toStringAsFixed(1),
                Icons.confirmation_number,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoTile(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.slate, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 20,
                color: AppColors.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.greyTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              // color: AppColors.dark,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDataMessage(String message) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          const Icon(
            Icons.info_outline,
            size: 48,
            color: AppColors.greyTextColor,
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.greyTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPlaceholderSections() {
    return Column(
      children: [
        _buildPlaceholderCard('Event Performance', Icons.analytics),
        const SizedBox(height: 16),
        _buildPlaceholderCard('Payment Methods', Icons.payment),
        const SizedBox(height: 16),
        _buildPlaceholderCard('Popular Tickets', Icons.star),
        const SizedBox(height: 16),
        _buildPlaceholderCard('Sales Timeline', Icons.timeline),
        const SizedBox(height: 16),
        _buildPlaceholderCard('Ticket Sales', Icons.sell),
      ],
    );
  }

  Widget _buildPlaceholderCard(String title, IconData icon) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: AppColors.greyTextColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    // color: AppColors.dark,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            _buildNoDataMessage('Data not available yet'),
          ],
        ),
      ),
    );
  }
}