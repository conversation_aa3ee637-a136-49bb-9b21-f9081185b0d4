import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:onekitty/controllers/events/edit_event_controller.dart';
import 'package:onekitty/models/events/events_model.dart';
import 'package:onekitty/models/events/media_models.dart';
import 'package:onekitty/models/events/categories_model.dart';
import 'package:onekitty/services/http_service.dart';

// Mock HttpService for testing
class MockHttpService extends HttpService {
  // Override methods as needed for testing
}

void main() {
  group('EditEventController Media Limit Tests', () {
    late EditEventController controller;

    setUp(() {
      Get.testMode = true;
      // Register mock dependencies
      Get.put<HttpService>(MockHttpService());
      controller = EditEventController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should enforce 3 media limit when loading event with more than 3 media', () {
      // Create test event with 5 media items
      final testMedia = List.generate(5, (index) => EventMedia(
        id: index + 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        eventId: 1,
        url: 'https://example.com/image${index + 1}.jpg',
        title: 'Test Media ${index + 1}',
        type: 'image',
      ));

      final testEvent = Event(
        id: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        deletedAt: null,
        title: 'Test Event',
        username: 'testuser',
        description: 'Test Description',
        email: '<EMAIL>',
        venue: 'Test Venue',
        latitude: 0.0,
        longitude: 0.0,
        locationTip: '',
        categoryId: 1,
        category: CategoriesModel(
          id: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          deletedAt: null,
          title: 'Test Category',
          description: 'Test Category Description',
          avatar: '',
        ),
        tickets: [],
        eventMedia: testMedia,
        socialAccounts: [],
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 1)),
      );

      final myEvent = MyEventsModel(event: testEvent);

      // Load event data
      controller.loadEventData(myEvent);

      // Verify media was trimmed to 3 items
      expect(controller.eventMedia.length, equals(3));
      expect(controller.hasMediaBeenTrimmed.value, isTrue);
      expect(controller.originalMediaCount.value, equals(5));
      expect(controller.isMediaLimitReached, isTrue);
      expect(controller.remainingMediaSlots, equals(0));
    });

    test('should not trim media when event has 3 or fewer media items', () {
      // Create test event with 2 media items
      final testMedia = List.generate(2, (index) => EventMedia(
        id: index + 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        eventId: 1,
        url: 'https://example.com/image${index + 1}.jpg',
        title: 'Test Media ${index + 1}',
        type: 'image',
      ));

      final testEvent = Event(
        id: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        deletedAt: null,
        title: 'Test Event',
        username: 'testuser',
        description: 'Test Description',
        email: '<EMAIL>',
        venue: 'Test Venue',
        latitude: 0.0,
        longitude: 0.0,
        locationTip: '',
        categoryId: 1,
        category: CategoriesModel(
          id: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          deletedAt: null,
          title: 'Test Category',
          description: 'Test Category Description',
          avatar: '',
        ),
        tickets: [],
        eventMedia: testMedia,
        socialAccounts: [],
        startDate: DateTime.now(),
        endDate: DateTime.now().add(const Duration(days: 1)),
      );

      final myEvent = MyEventsModel(event: testEvent);

      // Load event data
      controller.loadEventData(myEvent);

      // Verify media was not trimmed
      expect(controller.eventMedia.length, equals(2));
      expect(controller.hasMediaBeenTrimmed.value, isFalse);
      expect(controller.originalMediaCount.value, equals(2));
      expect(controller.isMediaLimitReached, isFalse);
      expect(controller.remainingMediaSlots, equals(1));
    });

    test('should provide correct media limit messages', () {
      // Test with no media
      expect(controller.getMediaLimitMessage(), equals('0/3 media items added'));
      
      // Test with media at limit
      controller.eventMedia.addAll(List.generate(3, (index) => EventMedia(
        id: index + 1,
        url: 'https://example.com/image${index + 1}.jpg',
      )));
      
      expect(controller.getMediaLimitMessage(), equals('Maximum 3 media items allowed'));
      expect(controller.isMediaLimitReached, isTrue);
    });

    test('should provide correct trim warning message', () {
      // Set up trimmed state
      controller.hasMediaBeenTrimmed.value = true;
      controller.originalMediaCount.value = 5;
      
      final warningMessage = controller.getMediaTrimWarningMessage();
      expect(warningMessage, contains('This event had 5 media items'));
      expect(warningMessage, contains('Only the first 3 have been kept'));
      expect(warningMessage, contains('Further modifications will permanently remove'));
    });
  });
}
